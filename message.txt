import os
import cv2
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from transformers import AutoProcessor, AutoModelForVision2Seq

# Load Kosmos-2
processor = AutoProcessor.from_pretrained("microsoft/kosmos-2-patch14-224")
model = AutoModelForVision2Seq.from_pretrained("microsoft/kosmos-2-patch14-224")
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)

# Paths
video_folders = {
    0: "/kaggle/input/shoplift/video pour mohamed/video normale",   # Label 0: Normal
    1: "/kaggle/input/shoplift/video pour mohamed/video vol"        # Label 1: Vol (Shoplifting)
}

# Extract frames from a video
def extract_frames(video_path, max_frames=5):
    cap = cv2.VideoCapture(video_path)
    frames = []
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    interval = max(1, total_frames // max_frames)
    
    for i in range(0, total_frames, interval):
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, frame = cap.read()
        if not ret:
            break
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frames.append(frame)
        if len(frames) >= max_frames:
            break
    cap.release()
    return frames

# Classify single frame
def classify_frame(frame):
    prompt = "This image shows"
    inputs = processor(images=frame, text=prompt, return_tensors="pt").to(device)
    outputs = model.generate(**inputs, max_new_tokens=20)
    caption = processor.batch_decode(outputs, skip_special_tokens=True)[0].lower()
    print("Caption:", caption)
    
    keywords = ["steal", "shoplifting", "theft", "robbery"]
    if any(word in caption for word in keywords):
        return 1  # Vol
    return 0  # Normal

# Classify entire video
def classify_video(video_path):
    frames = extract_frames(video_path)
    if not frames:
        return 0  # Default to Normal if no frames
    preds = [classify_frame(frame) for frame in frames]
    return int(sum(preds) > len(preds) / 2)

# Evaluation
true_labels = []
pred_labels = []

for label, folder in video_folders.items():
    for video_file in os.listdir(folder):
        video_path = os.path.join(folder, video_file)
        pred = classify_video(video_path)
        pred_labels.append(pred)
        true_labels.append(label)
        print(f"Video: {video_file} ➔ Predicted: {'Vol' if pred else 'Normal'} | True: {'Vol' if label else 'Normal'}")

# Compute Metrics
accuracy = accuracy_score(true_labels, pred_labels)
precision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary')

print("\n📊 Evaluation Metrics:")
print(f"✅ Accuracy: {accuracy:.4f}")
print(f"🎯 Precision: {precision:.4f}")
print(f"🔄 Recall: {recall:.4f}")
print(f"🏅 F1 Score: {f1:.4f}")