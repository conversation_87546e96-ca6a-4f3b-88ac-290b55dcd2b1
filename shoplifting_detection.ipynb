{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install transformers accelerate torchvision opencv-python scikit-learn\n", "!pip install git+https://github.com/huggingface/transformers\n", "\n", "# Clone and install flamingo-mini\n", "import os\n", "if not os.path.exists('flamingo-mini'):\n", "    !git clone https://github.com/dhansmair/flamingo-mini\n", "    \n", "# Change to flamingo-mini directory and install\n", "%cd flamingo-mini\n", "!pip install -e .\n", "%cd ..\n", "\n", "print(\"✅ Installation complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "sys.path.append('flamingo-mini')\n", "import cv2\n", "import torch\n", "import numpy as np\n", "from PIL import Image\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix\n", "from flamingo_mini import FlamingoModel, FlamingoProcessor\n", "import time\n", "\n", "# Load model and processor\n", "model = FlamingoModel.from_pretrained(\"dhansmair/flamingo-mini\")\n", "processor = FlamingoProcessor(model.config)\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model = model.to(device)\n", "\n", "# Folder paths\n", "video_folders = {\n", "    0: r\"C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale\",\n", "    1: r\"C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video vol\"\n", "}\n", "\n", "# Frame extractor\n", "def extract_frames(video_path, max_frames=8):\n", "    cap = cv2.VideoCapture(video_path)\n", "    frames = []\n", "    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))\n", "    fps = cap.get(cv2.CAP_PROP_FPS)\n", "    duration = total_frames / fps if fps > 0 else 0\n", "    interval = max(1, total_frames // max_frames)\n", "\n", "    for i in range(0, total_frames, interval):\n", "        cap.set(cv2.CAP_PROP_POS_FRAMES, i)\n", "        ret, frame = cap.read()\n", "        if not ret:\n", "            break\n", "        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "        frames.append(Image.fromarray(rgb))\n", "        if len(frames) >= max_frames:\n", "            break\n", "    cap.release()\n", "    return frames, total_frames, fps, duration\n", "\n", "# Frame classifier using captioning\n", "def classify_frame(image):\n", "    prompt = \"Question: Is there shoplifting in this video? Answer:\"\n", "    inputs = processor(images=image, text=prompt, return_tensors=\"pt\").to(device)\n", "    outputs = model.generate(**inputs, max_new_tokens=20)\n", "    caption = processor.batch_decode(outputs, skip_special_tokens=True)[0].lower()\n", "    \n", "    response_text = caption.split(\"answer:\")[-1].strip()\n", "    \n", "    keywords = [\"steal\", \"shoplifting\", \"theft\", \"robbery\", \"hiding\", \"suspicious\"]\n", "    is_theft = int(any(word in response_text for word in keywords))\n", "    \n", "    return is_theft, response_text\n", "\n", "# Video classifier (majority vote)\n", "def classify_video(video_path):\n", "    frames, total_frames, fps, duration = extract_frames(video_path)\n", "    if not frames:\n", "        return 0, \"No frames extracted\", 0, 0, 0, 0\n", "    \n", "    start_time = time.time()\n", "    preds = []\n", "    responses = []\n", "    for f in frames:\n", "        pred, response = classify_frame(f)\n", "        preds.append(pred)\n", "        responses.append(response)\n", "        \n", "    processing_time = time.time() - start_time\n", "    \n", "    final_prediction = int(sum(preds) > len(preds) / 2)\n", "    \n", "    # For simplicity, we'll just use the first detailed response for the output\n", "    final_response = responses[0] if responses else \"No response generated.\"\n", "    \n", "    return final_prediction, final_response, processing_time, total_frames, fps, duration, len(frames)\n", "\n", "# Evaluation loop\n", "true_labels = []\n", "pred_labels = []\n", "total_videos = sum(len(os.listdir(folder)) for folder in video_folders.values())\n", "processed_videos = 0\n", "total_time = 0\n", "\n", "for label, folder in video_folders.items():\n", "    class_name = 'Theft' if label == 1 else 'Normal'\n", "    print(f\"================================================================================\")\n", "    print(f\"✅ EVALUATING {class_name.upper()} VIDEOS ({'Positive' if label == 1 else 'Negative'} Class)\")\n", "    print(f\"================================================================================\")\n", "    for video_file in os.listdir(folder):\n", "        processed_videos += 1\n", "        video_path = os.path.join(folder, video_file)\n", "        \n", "        print(f\"[{processed_videos}/{total_videos}] {'🚨' if label == 1 else '✅'} {class_name} Video {processed_videos}: {video_file}\")\n", "        print(\"\\n==================================================\")\n", "        print(f\"Testing video: {video_path}\")\n", "        print(f\"Question: Is there shoplifting in this video?\")\n", "        print(\"==================================================\")\n", "        \n", "        pred, response, proc_time, total_frames, fps, duration, extracted_frame_count = classify_video(video_path)\n", "        total_time += proc_time\n", "        \n", "        print(f\"Extracting frames from: {video_path}\")\n", "        print(f\"Video info: {total_frames} frames, {fps:.2f} FPS, {duration:.2f}s duration\")\n", "        print(f\"Extracted {extracted_frame_count} frames\\n\")\n", "        \n", "        print(f\"Response (generated in {proc_time:.2f}s):\")\n", "        print(\"----------------------------------------\")\n", "        print(response)\n", "        print(\"----------------------------------------\")\n", "        \n", "        pred_labels.append(pred)\n", "        true_labels.append(label)\n", "        \n", "        ground_truth_str = 'THEFT' if label == 1 else 'NORMAL'\n", "        prediction_str = 'THEFT' if pred == 1 else 'NORMAL'\n", "        correctness_str = '✅ CORRECT' if label == pred else ('❌ FALSE POSITIVE' if label == 0 and pred == 1 else '❌ FALSE NEGATIVE')\n", "        \n", "        print(f\"   Ground Truth: {ground_truth_str} | Prediction: {prediction_str} | {correctness_str}\")\n", "        print(f\"   Time: {proc_time:.2f}s\\n\")\n", "\n", "# Compute Metrics\n", "accuracy = accuracy_score(true_labels, pred_labels)\n", "precision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary', zero_division=0)\n", "tn, fp, fn, tp = confusion_matrix(true_labels, pred_labels).ravel()\n", "specificity = tn / (tn + fp) if (tn + fp) > 0 else 0\n", "avg_time = total_time / total_videos if total_videos > 0 else 0\n", "\n", "print(\"================================================================================\")\n", "print(\"📊 PERFORMANCE METRICS CALCULATION\")\n", "print(\"================================================================================\")\n", "print(\"\\n🎯 FINAL RESULTS:\")\n", "print(\"==================================================\")\n", "print(f\"📈 F1 SCORE:           {f1:.4f} ({f1:.2%})\")\n", "print(f\"🎯 PRECISION:          {precision:.4f} ({precision:.2%})\")\n", "print(f\"🔍 RECALL:             {recall:.4f} ({recall:.2%})\")\n", "print(f\"✅ ACCURACY:           {accuracy:.4f} ({accuracy:.2%})\")\n", "print(f\"🛡️ SPECIFICITY:        {specificity:.4f} ({specificity:.2%})\")\n", "print(f\"⏱️ AVG PROCESSING TIME: {avg_time:.2f} seconds/video\")\n", "\n", "print(\"📊 CONFUSION MATRIX:\")\n", "print(\"==============================\")\n", "print(\"                 Predicted\")\n", "print(\"               Normal  Theft\")\n", "print(f\"Actual Normal    {tn:4d}    {fp:4d}\")\n", "print(f\"       Theft     {fn:4d}    {tp:4d}\")\n", "\n", "print(\"📋 DETAILED BREAKDOWN:\")\n", "print(\"========================================\")\n", "print(f\"✅ True Positives (TP):   {tp} - Theft correctly detected\")\n", "print(f\"✅ True Negatives (TN):    {tn} - Normal correctly identified\")\n", "print(f\"❌ False Positives (FP):  {fp} - Normal wrongly flagged as theft\")\n", "print(f\"❌ False Negatives (FN):   {fn} - Theft missed\")\n", "\n", "print(\"🚀 EVALUATION COMPLETE!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 4}