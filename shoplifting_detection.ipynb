!pip install transformers accelerate torchvision opencv-python
!pip install git+https://github.com/huggingface/transformers accelerate torchvision
import os
if not os.path.exists('flamingo-mini'):
    !git clone https://github.com/dhansmair/flamingo-mini
%cd flamingo-mini
!pip install -e .
%cd ..

import os
import sys
sys.path.append('flamingo-mini')
import cv2
import torch
import numpy as np
from PIL import Image
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
from flamingo_mini import FlamingoModel, FlamingoProcessor
import time

# Load model and processor
model = FlamingoModel.from_pretrained("dhansmair/flamingo-mini")
processor = FlamingoProcessor.from_pretrained("dhansmair/flamingo-mini")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)

# Folder paths
video_folders = {
    0: r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",
    1: r"C:\Users\<USER>\Desktop\video pour mohamed\video vol"
}

# Frame extractor
def extract_frames(video_path, max_frames=8):
    cap = cv2.VideoCapture(video_path)
    frames = []
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    duration = total_frames / fps if fps > 0 else 0
    interval = max(1, total_frames // max_frames)

    for i in range(0, total_frames, interval):
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, frame = cap.read()
        if not ret:
            break
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frames.append(Image.fromarray(rgb))
        if len(frames) >= max_frames:
            break
    cap.release()
    return frames, total_frames, fps, duration

# Frame classifier using captioning
def classify_frame(image):
    prompt = "Question: Is there shoplifting in this video? Answer:"
    inputs = processor(images=image, text=prompt, return_tensors="pt").to(device)
    outputs = model.generate(**inputs, max_new_tokens=20)
    caption = processor.batch_decode(outputs, skip_special_tokens=True)[0].lower()
    
    response_text = caption.split("answer:")[-1].strip()
    
    keywords = ["steal", "shoplifting", "theft", "robbery", "hiding", "suspicious"]
    is_theft = int(any(word in response_text for word in keywords))
    
    return is_theft, response_text

# Video classifier (majority vote)
def classify_video(video_path):
    frames, total_frames, fps, duration = extract_frames(video_path)
    if not frames:
        return 0, "No frames extracted", 0, 0, 0, 0
    
    start_time = time.time()
    preds = []
    responses = []
    for f in frames:
        pred, response = classify_frame(f)
        preds.append(pred)
        responses.append(response)
        
    processing_time = time.time() - start_time
    
    final_prediction = int(sum(preds) > len(preds) / 2)
    
    # For simplicity, we'll just use the first detailed response for the output
    final_response = responses[0] if responses else "No response generated."
    
    return final_prediction, final_response, processing_time, total_frames, fps, duration, len(frames)

# Evaluation loop
true_labels = []
pred_labels = []
total_videos = sum(len(os.listdir(folder)) for folder in video_folders.values())
processed_videos = 0
total_time = 0

for label, folder in video_folders.items():
    class_name = 'Theft' if label == 1 else 'Normal'
    print(f"================================================================================")
    print(f"✅ EVALUATING {class_name.upper()} VIDEOS ({'Positive' if label == 1 else 'Negative'} Class)")
    print(f"================================================================================")
    for video_file in os.listdir(folder):
        processed_videos += 1
        video_path = os.path.join(folder, video_file)
        
        print(f"[{processed_videos}/{total_videos}] {'🚨' if label == 1 else '✅'} {class_name} Video {processed_videos}: {video_file}")
        print("\n==================================================")
        print(f"Testing video: {video_path}")
        print(f"Question: Is there shoplifting in this video?")
        print("==================================================")
        
        pred, response, proc_time, total_frames, fps, duration, extracted_frame_count = classify_video(video_path)
        total_time += proc_time
        
        print(f"Extracting frames from: {video_path}")
        print(f"Video info: {total_frames} frames, {fps:.2f} FPS, {duration:.2f}s duration")
        print(f"Extracted {extracted_frame_count} frames\n")
        
        print(f"Response (generated in {proc_time:.2f}s):")
        print("----------------------------------------")
        print(response)
        print("----------------------------------------")
        
        pred_labels.append(pred)
        true_labels.append(label)
        
        ground_truth_str = 'THEFT' if label == 1 else 'NORMAL'
        prediction_str = 'THEFT' if pred == 1 else 'NORMAL'
        correctness_str = '✅ CORRECT' if label == pred else ('❌ FALSE POSITIVE' if label == 0 and pred == 1 else '❌ FALSE NEGATIVE')
        
        print(f"   Ground Truth: {ground_truth_str} | Prediction: {prediction_str} | {correctness_str}")
        print(f"   Time: {proc_time:.2f}s\n")

# Compute Metrics
accuracy = accuracy_score(true_labels, pred_labels)
precision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary', zero_division=0)
tn, fp, fn, tp = confusion_matrix(true_labels, pred_labels).ravel()
specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
avg_time = total_time / total_videos if total_videos > 0 else 0

print("================================================================================")
print("📊 PERFORMANCE METRICS CALCULATION")
print("================================================================================")
print("\n🎯 FINAL RESULTS:")
print("==================================================")
print(f"📈 F1 SCORE:           {f1:.4f} ({f1:.2%})")
print(f"🎯 PRECISION:          {precision:.4f} ({precision:.2%})")
print(f"🔍 RECALL:             {recall:.4f} ({recall:.2%})")
print(f"✅ ACCURACY:           {accuracy:.4f} ({accuracy:.2%})")
print(f"🛡️ SPECIFICITY:        {specificity:.4f} ({specificity:.2%})")
print(f"⏱️ AVG PROCESSING TIME: {avg_time:.2f} seconds/video")

print("📊 CONFUSION MATRIX:")
print("==============================")
print("                 Predicted")
print("               Normal  Theft")
print(f"Actual Normal    {tn:4d}    {fp:4d}")
print(f"       Theft     {fn:4d}    {tp:4d}")

print("📋 DETAILED BREAKDOWN:")
print("========================================")
print(f"✅ True Positives (TP):   {tp} - Theft correctly detected")
print(f"✅ True Negatives (TN):    {tn} - Normal correctly identified")
print(f"❌ False Positives (FP):  {fp} - Normal wrongly flagged as theft")
print(f"❌ False Negatives (FN):   {fn} - Theft missed")

print("🚀 EVALUATION COMPLETE!")