!pip install transformers accelerate torchvision opencv-python


!pip install git+https://github.com/huggingface/transformers accelerate torchvision
!git clone https://github.com/dhansmair/flamingo-mini
%cd flamingo-mini
!pip install -e .


import os
import cv2
import torch
from PIL import Image
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from flamingo_mini import FlamingoForConditionalGeneration, FlamingoProcessor

# Load model and processor
model = FlamingoForConditionalGeneration.from_pretrained("dhansmair/flamingo-mini")
processor = FlamingoProcessor.from_pretrained("dhansmair/flamingo-mini")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)


# ✅ Folder paths (replace with your own Google Drive/Kaggle paths)
video_folders = {
    0: "/kaggle/input/shoplifting/video normale",
    1: "/kaggle/input/shoplifting/video vol"
}


# ✅ Frame extractor
def extract_frames(video_path, max_frames=5):
    cap = cv2.VideoCapture(video_path)
    frames = []
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    interval = max(1, total_frames // max_frames)

    for i in range(0, total_frames, interval):
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, frame = cap.read()
        if not ret:
            break
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frames.append(Image.fromarray(rgb))
        if len(frames) >= max_frames:
            break
    cap.release()
    return frames

# ✅ Frame classifier using captioning
def classify_frame(image):
    prompt = "A description of what is happening in the image:"
    inputs = processor(images=image, text=prompt, return_tensors="pt").to(device)
    outputs = model.generate(**inputs, max_new_tokens=50)
    caption = processor.batch_decode(outputs, skip_special_tokens=True)[0].lower()
    print("🖼️ Caption:", caption)

    keywords = ["steal", "shoplifting", "theft", "robbery", "hiding", "suspicious"]
    return int(any(word in caption for word in keywords))

# ✅ Video classifier (majority vote)
def classify_video(video_path):
    frames = extract_frames(video_path)
    if not frames:
        return 0
    preds = [classify_frame(f) for f in frames]
    return int(sum(preds) > len(preds) / 2)

# ✅ Evaluation loop
true_labels = []
pred_labels = []

for label, folder in video_folders.items():
    for video_file in os.listdir(folder):
        video_path = os.path.join(folder, video_file)
        pred = classify_video(video_path)
        pred_labels.append(pred)
        true_labels.append(label)
        print(f"🎥 {video_file} ➜ Predicted: {'Vol' if pred else 'Normal'} | True: {'Vol' if label else 'Normal'}")

# ✅ Print metrics
accuracy = accuracy_score(true_labels, pred_labels)
precision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary')

print("\n📊 Evaluation Metrics:")
print(f"✅ Accuracy: {accuracy:.4f}")
print(f"🎯 Precision: {precision:.4f}")
print(f"🔄 Recall: {recall:.4f}")
print(f"🏅 F1 Score: {f1:.4f}")


