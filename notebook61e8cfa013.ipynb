{"metadata": {"kernelspec": {"name": "python3", "display_name": "Python 3", "language": "python"}, "language_info": {"name": "python", "version": "3.11.11", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "colab": {"machine_shape": "hm", "gpuType": "T4"}, "accelerator": "GPU", "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"sourceId": 12366475, "sourceType": "datasetVersion", "datasetId": 7797021}], "dockerImageVersionId": 31041, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "!pip install transformers accelerate torchvision opencv-python\n", "metadata": {"colab_type": "code", "trusted": true, "execution": {"iopub.status.busy": "2025-07-07T11:01:25.594618Z", "iopub.execute_input": "2025-07-07T11:01:25.595191Z", "iopub.status.idle": "2025-07-07T11:02:33.896560Z", "shell.execute_reply.started": "2025-07-07T11:01:25.595168Z", "shell.execute_reply": "2025-07-07T11:02:33.895499Z"}}, "outputs": [{"name": "stdout", "text": "Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.53.1)\nRequirement already satisfied: accelerate in /usr/local/lib/python3.11/dist-packages (1.5.2)\nRequirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\nRequirement already satisfied: opencv-python in /usr/local/lib/python3.11/dist-packages (*********)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\nRequirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.31.1)\nRequirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (1.26.4)\nRequirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (25.0)\nRequirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\nRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\nRequirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\nRequirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\nRequirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\nRequirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\nRequirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate) (7.0.0)\nRequirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from accelerate) (2.6.0+cu124)\nRequirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision) (11.1.0)\nRequirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (4.13.2)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.4.2)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.1.6)\nRequirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2025.3.2)\nRequirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nCollecting nvidia-cudnn-cu12==******** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cublas-cu12==******** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cufft-cu12==******** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-curand-cu12==********** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cusolver-cu12==******** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cusparse-cu12==********** (from torch>=2.0.0->accelerate)\n  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\nRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nCollecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=2.0.0->accelerate)\n  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=2.0.0->accelerate) (1.3.0)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.0)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (2025.1.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (2022.1.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers) (2.4.1)\nRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.4.26)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.0.0->accelerate) (3.0.2)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers) (2022.1.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.17->transformers) (1.3.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.17->transformers) (2024.2.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.17->transformers) (2024.2.0)\nDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m32.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m13.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m73.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12\n  Attempting uninstall: nvidia-nvjitlink-cu12\n    Found existing installation: nvidia-nvjitlink-cu12 12.9.41\n    Uninstalling nvidia-nvjitlink-cu12-12.9.41:\n      Successfully uninstalled nvidia-nvjitlink-cu12-12.9.41\n  Attempting uninstall: nvidia-curand-cu12\n    Found existing installation: nvidia-curand-cu12 10.3.10.19\n    Uninstalling nvidia-curand-cu12-10.3.10.19:\n      Successfully uninstalled nvidia-curand-cu12-10.3.10.19\n  Attempting uninstall: nvidia-cufft-cu12\n    Found existing installation: nvidia-cufft-cu12 11.4.0.6\n    Uninstalling nvidia-cufft-cu12-11.4.0.6:\n      Successfully uninstalled nvidia-cufft-cu12-11.4.0.6\n  Attempting uninstall: nvidia-cublas-cu12\n    Found existing installation: nvidia-cublas-cu12 12.9.0.13\n    Uninstalling nvidia-cublas-cu12-12.9.0.13:\n      Successfully uninstalled nvidia-cublas-cu12-12.9.0.13\n  Attempting uninstall: nvidia-cusparse-cu12\n    Found existing installation: nvidia-cusparse-cu12 12.5.9.5\n    Uninstalling nvidia-cusparse-cu12-12.5.9.5:\n      Successfully uninstalled nvidia-cusparse-cu12-12.5.9.5\n  Attempting uninstall: nvidia-cudnn-cu12\n    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n  Attempting uninstall: nvidia-cusolver-cu12\n    Found existing installation: nvidia-cusolver-cu12 *********\n    Uninstalling nvidia-cusolver-cu12-*********:\n      Successfully uninstalled nvidia-cusolver-cu12-*********\nSuccessfully installed nvidia-cublas-cu12-******** nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127\n", "output_type": "stream"}], "execution_count": 4}, {"cell_type": "code", "source": "!pip install git+https://github.com/huggingface/transformers accelerate torchvision\n!git clone https://github.com/dhansmair/flamingo-mini\n%cd flamingo-mini\n!pip install -e .\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-07T11:05:58.951142Z", "iopub.execute_input": "2025-07-07T11:05:58.951820Z", "iopub.status.idle": "2025-07-07T11:06:35.825528Z", "shell.execute_reply.started": "2025-07-07T11:05:58.951797Z", "shell.execute_reply": "2025-07-07T11:06:35.824519Z"}}, "outputs": [{"name": "stdout", "text": "Collecting git+https://github.com/huggingface/transformers\n  Cloning https://github.com/huggingface/transformers to /tmp/pip-req-build-4j_o23lr\n  Running command git clone --filter=blob:none --quiet https://github.com/huggingface/transformers /tmp/pip-req-build-4j_o23lr\n  Resolved https://github.com/huggingface/transformers to commit b283d52f7f89d9cf3c77cfef233c4cbf700959ff\n  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: accelerate in /usr/local/lib/python3.11/dist-packages (1.5.2)\nRequirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (3.18.0)\nRequirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (0.31.1)\nRequirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (1.26.4)\nRequirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (25.0)\nRequirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (6.0.2)\nRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (2024.11.6)\nRequirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (2.32.3)\nRequirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (0.21.1)\nRequirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (0.5.3)\nRequirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers==4.54.0.dev0) (4.67.1)\nRequirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate) (7.0.0)\nRequirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from accelerate) (2.6.0+cu124)\nRequirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision) (11.1.0)\nRequirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (4.13.2)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.4.2)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.1.6)\nRequirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2025.3.2)\nRequirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (********)\nRequirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (********)\nRequirement already satisfied: nvidia-cufft-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (********)\nRequirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (**********)\nRequirement already satisfied: nvidia-cusolver-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (********)\nRequirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (**********)\nRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\nRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=2.0.0->accelerate) (1.3.0)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers==4.54.0.dev0) (1.1.0)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (2025.1.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (2022.1.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->transformers==4.54.0.dev0) (2.4.1)\nRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.54.0.dev0) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.54.0.dev0) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.54.0.dev0) (2.4.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers==4.54.0.dev0) (2025.4.26)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.0.0->accelerate) (3.0.2)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers==4.54.0.dev0) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->transformers==4.54.0.dev0) (2022.1.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.17->transformers==4.54.0.dev0) (1.3.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.17->transformers==4.54.0.dev0) (2024.2.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.17->transformers==4.54.0.dev0) (2024.2.0)\nBuilding wheels for collected packages: transformers\n  Building wheel for transformers (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n  Created wheel for transformers: filename=transformers-4.54.0.dev0-py3-none-any.whl size=11759380 sha256=c296197e5c7c438e6661a9c533b8fbdbe6fa9a2c50df4367494c1e346fbe9c6e\n  Stored in directory: /tmp/pip-ephem-wheel-cache-i9g5jh_5/wheels/04/a3/f1/b88775f8e1665827525b19ac7590250f1038d947067beba9fb\nSuccessfully built transformers\nInstalling collected packages: transformers\n  Attempting uninstall: transformers\n    Found existing installation: transformers 4.53.1\n    Uninstalling transformers-4.53.1:\n      Successfully uninstalled transformers-4.53.1\nSuccessfully installed transformers-4.54.0.dev0\nCloning into 'flamingo-mini'...\nremote: Enumerating objects: 305, done.\u001b[K\nremote: Counting objects: 100% (102/102), done.\u001b[K\nremote: Compressing objects: 100% (34/34), done.\u001b[K\nremote: Total 305 (delta 90), reused 68 (delta 68), pack-reused 203 (from 1)\u001b[K\nReceiving objects: 100% (305/305), 1.40 MiB | 10.58 MiB/s, done.\nResolving deltas: 100% (196/196), done.\n/kaggle/working/flamingo-mini\nObtaining file:///kaggle/working/flamingo-mini\n  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\nRequirement already satisfied: einops>=0.4 in /usr/local/lib/python3.11/dist-packages (from flamingo-mini==0.0.2) (0.8.1)\nCollecting einops-exts (from flamingo-mini==0.0.2)\n  Downloading einops_exts-0.0.4-py3-none-any.whl.metadata (621 bytes)\nRequirement already satisfied: torch>=1.6 in /usr/local/lib/python3.11/dist-packages (from flamingo-mini==0.0.2) (2.6.0+cu124)\nRequirement already satisfied: transformers>=4.25.1 in /usr/local/lib/python3.11/dist-packages (from flamingo-mini==0.0.2) (4.54.0.dev0)\nRequirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from flamingo-mini==0.0.2) (1.26.4)\nRequirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (from flamingo-mini==0.0.2) (11.1.0)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (3.18.0)\nRequirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (4.13.2)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (3.4.2)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (3.1.6)\nRequirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (2025.3.2)\nRequirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (12.4.127)\nRequirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (12.4.127)\nRequirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (12.4.127)\nRequirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (********)\nRequirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (********)\nRequirement already satisfied: nvidia-cufft-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (********)\nRequirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (**********)\nRequirement already satisfied: nvidia-cusolver-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (********)\nRequirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (**********)\nRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (12.4.127)\nRequirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (12.4.127)\nRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.6->flamingo-mini==0.0.2) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.6->flamingo-mini==0.0.2) (1.3.0)\nRequirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (0.31.1)\nRequirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (25.0)\nRequirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (6.0.2)\nRequirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (2024.11.6)\nRequirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (2.32.3)\nRequirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (0.21.1)\nRequirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (0.5.3)\nRequirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.25.1->flamingo-mini==0.0.2) (4.67.1)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (2025.1.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (2022.1.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->flamingo-mini==0.0.2) (2.4.1)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers>=4.25.1->flamingo-mini==0.0.2) (1.1.0)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.6->flamingo-mini==0.0.2) (3.0.2)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->flamingo-mini==0.0.2) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->flamingo-mini==0.0.2) (2022.1.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->flamingo-mini==0.0.2) (1.3.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->flamingo-mini==0.0.2) (2024.2.0)\nRequirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers>=4.25.1->flamingo-mini==0.0.2) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers>=4.25.1->flamingo-mini==0.0.2) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers>=4.25.1->flamingo-mini==0.0.2) (2.4.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers>=4.25.1->flamingo-mini==0.0.2) (2025.4.26)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->flamingo-mini==0.0.2) (2024.2.0)\nDownloading einops_exts-0.0.4-py3-none-any.whl (3.9 kB)\nInstalling collected packages: einops-exts, flamingo-mini\n  Running setup.py develop for flamingo-mini\nSuccessfully installed einops-exts-0.0.4 flamingo-mini-0.0.2\n", "output_type": "stream"}], "execution_count": 9}, {"cell_type": "markdown", "source": "## Local Inference on GPU \nModel page: https://huggingface.co/dhansmair/flamingo-mini\n\n⚠️ If the generated code snippets do not work, please open an issue on either the [model repo](https://huggingface.co/dhansmair/flamingo-mini)\n\t\t\tand/or on [huggingface.js](https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/src/model-libraries-snippets.ts) 🙏", "metadata": {"colab_type": "text"}}, {"cell_type": "code", "source": "import os\nimport cv2\nimport torch\nfrom PIL import Image\nfrom sklearn.metrics import accuracy_score, precision_recall_fscore_support\nfrom flamingo_mini import FlamingoForConditionalGeneration, FlamingoProcessor\n\n# Load model and processor\nmodel = FlamingoForConditionalGeneration.from_pretrained(\"dhansmair/flamingo-mini\")\nprocessor = FlamingoProcessor.from_pretrained(\"dhansmair/flamingo-mini\")\n\ndevice = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\nmodel = model.to(device)\n\n\n# ✅ Folder paths (replace with your own Google Drive/Kaggle paths)\nvideo_folders = {\n    0: \"/kaggle/input/shoplifting/video normale\",\n    1: \"/kaggle/input/shoplifting/video vol\"\n}\n\n\n# ✅ Frame extractor\ndef extract_frames(video_path, max_frames=5):\n    cap = cv2.VideoCapture(video_path)\n    frames = []\n    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))\n    interval = max(1, total_frames // max_frames)\n\n    for i in range(0, total_frames, interval):\n        cap.set(cv2.CAP_PROP_POS_FRAMES, i)\n        ret, frame = cap.read()\n        if not ret:\n            break\n        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n        frames.append(Image.fromarray(rgb))\n        if len(frames) >= max_frames:\n            break\n    cap.release()\n    return frames\n\n# ✅ Frame classifier using captioning\ndef classify_frame(image):\n    prompt = \"A description of what is happening in the image:\"\n    inputs = processor(images=image, text=prompt, return_tensors=\"pt\").to(device)\n    outputs = model.generate(**inputs, max_new_tokens=50)\n    caption = processor.batch_decode(outputs, skip_special_tokens=True)[0].lower()\n    print(\"🖼️ Caption:\", caption)\n\n    keywords = [\"steal\", \"shoplifting\", \"theft\", \"robbery\", \"hiding\", \"suspicious\"]\n    return int(any(word in caption for word in keywords))\n\n# ✅ Video classifier (majority vote)\ndef classify_video(video_path):\n    frames = extract_frames(video_path)\n    if not frames:\n        return 0\n    preds = [classify_frame(f) for f in frames]\n    return int(sum(preds) > len(preds) / 2)\n\n# ✅ Evaluation loop\ntrue_labels = []\npred_labels = []\n\nfor label, folder in video_folders.items():\n    for video_file in os.listdir(folder):\n        video_path = os.path.join(folder, video_file)\n        pred = classify_video(video_path)\n        pred_labels.append(pred)\n        true_labels.append(label)\n        print(f\"🎥 {video_file} ➜ Predicted: {'Vol' if pred else 'Normal'} | True: {'Vol' if label else 'Normal'}\")\n\n# ✅ Print metrics\naccuracy = accuracy_score(true_labels, pred_labels)\nprecision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary')\n\nprint(\"\\n📊 Evaluation Metrics:\")\nprint(f\"✅ Accuracy: {accuracy:.4f}\")\nprint(f\"🎯 Precision: {precision:.4f}\")\nprint(f\"🔄 Recall: {recall:.4f}\")\nprint(f\"🏅 F1 Score: {f1:.4f}\")\n", "metadata": {"colab_type": "code", "trusted": true, "execution": {"iopub.status.busy": "2025-07-07T11:15:12.569915Z", "iopub.execute_input": "2025-07-07T11:15:12.570684Z", "iopub.status.idle": "2025-07-07T11:15:12.596844Z", "shell.execute_reply.started": "2025-07-07T11:15:12.570663Z", "shell.execute_reply": "2025-07-07T11:15:12.595907Z"}}, "outputs": [{"traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_35/4008187651.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mPIL\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mImage\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0msklearn\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmetrics\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0maccuracy_score\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mprecision_recall_fscore_support\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 6\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mflamingo_mini\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mFlamingoForConditionalGeneration\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mFlamingoProcessor\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      7\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[0;31m# Load model and processor\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'FlamingoForConditionalGeneration' from 'flamingo_mini' (/kaggle/working/flamingo-mini/flamingo_mini/__init__.py)"], "ename": "ImportError", "evalue": "cannot import name 'FlamingoForConditionalGeneration' from 'flamingo_mini' (/kaggle/working/flamingo-mini/flamingo_mini/__init__.py)", "output_type": "error"}], "execution_count": 14}, {"cell_type": "code", "source": "", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}