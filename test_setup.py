#!/usr/bin/env python3
"""
Test script to verify the Flamingo-Mini setup works correctly
"""

import os
import sys

def test_imports():
    """Test if all required packages can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL/Pillow imported successfully")
    except ImportError as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    try:
        from sklearn.metrics import accuracy_score
        print("✅ Scikit-learn imported successfully")
    except ImportError as e:
        print(f"❌ Scikit-learn import failed: {e}")
        return False
    
    return True

def test_flamingo():
    """Test if Flamingo-Mini can be imported and loaded"""
    print("\n🔍 Testing Flamingo-Mini...")
    
    # Add flamingo-mini to path if it exists
    if os.path.exists('flamingo-mini'):
        sys.path.append('flamingo-mini')
    
    try:
        from flamingo_mini import FlamingoModel, FlamingoProcessor
        print("✅ Flamingo-Mini imports successful")
    except ImportError as e:
        print(f"❌ Flamingo-Mini import failed: {e}")
        print("💡 Make sure you've cloned and installed flamingo-mini:")
        print("   git clone https://github.com/dhansmair/flamingo-mini")
        print("   cd flamingo-mini && pip install -e .")
        return False
    
    try:
        print("🔄 Loading Flamingo model (this may take a while)...")
        model = FlamingoModel.from_pretrained("dhansmair/flamingo-mini")
        processor = FlamingoProcessor(model.config)
        print("✅ Flamingo model loaded successfully")
        return True
    except Exception as e:
        print(f"❌ Flamingo model loading failed: {e}")
        return False

def test_dataset_paths():
    """Test if dataset paths exist"""
    print("\n🔍 Testing dataset paths...")
    
    video_folders = {
        0: r"C:\Users\<USER>\Desktop\video pour mohamed\video normale",
        1: r"C:\Users\<USER>\Desktop\video pour mohamed\video vol"
    }
    
    all_exist = True
    for label, folder in video_folders.items():
        if os.path.exists(folder):
            video_count = len([f for f in os.listdir(folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))])
            print(f"✅ Found {video_count} videos in {'theft' if label == 1 else 'normal'} folder")
        else:
            print(f"❌ Folder not found: {folder}")
            all_exist = False
    
    if not all_exist:
        print("💡 Please update the dataset paths in the notebook to match your actual dataset location")
    
    return all_exist

def main():
    """Run all tests"""
    print("🧪 Running setup tests for Flamingo-Mini Shoplifting Detection\n")
    
    # Test basic imports
    imports_ok = test_imports()
    
    # Test Flamingo-Mini
    flamingo_ok = test_flamingo()
    
    # Test dataset paths
    dataset_ok = test_dataset_paths()
    
    print("\n" + "="*60)
    print("📋 TEST SUMMARY:")
    print("="*60)
    print(f"Basic imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Flamingo-Mini: {'✅ PASS' if flamingo_ok else '❌ FAIL'}")
    print(f"Dataset paths: {'✅ PASS' if dataset_ok else '⚠️ WARNING'}")
    
    if imports_ok and flamingo_ok:
        print("\n🎉 Setup is ready! You can run the notebook.")
        if not dataset_ok:
            print("⚠️ Remember to update dataset paths in the notebook.")
    else:
        print("\n❌ Setup incomplete. Please fix the issues above.")

if __name__ == "__main__":
    main()
