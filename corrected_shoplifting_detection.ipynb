{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Shoplifting Detection using Flamingo-Mini\n", "\n", "This notebook uses the Flamingo-Mini model to detect shoplifting in videos by:\n", "1. Extracting frames from videos\n", "2. Generating captions for each frame\n", "3. Analyzing captions for theft-related keywords\n", "4. Making final decisions based on majority voting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install transformers accelerate torchvision opencv-python scikit-learn\n", "!pip install git+https://github.com/huggingface/transformers\n", "\n", "# Clone and install flamingo-mini\n", "import os\n", "if not os.path.exists('flamingo-mini'):\n", "    !git clone https://github.com/dhansmair/flamingo-mini\n", "    \n", "# Change to flamingo-mini directory and install\n", "%cd flamingo-mini\n", "!pip install -e .\n", "%cd ..\n", "\n", "print(\"✅ Installation complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "sys.path.append('flamingo-mini')\n", "import cv2\n", "import torch\n", "import numpy as np\n", "from PIL import Image\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix\n", "from flamingo_mini import FlamingoModel, FlamingoProcessor\n", "import time\n", "\n", "print(\"🔄 Loading Flamingo model...\")\n", "# Load model and processor\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"📱 Using device: {device}\")\n", "\n", "model = FlamingoModel.from_pretrained(\"dhansmair/flamingo-mini\")\n", "model.to(device)\n", "model.eval()\n", "\n", "# Correct processor initialization\n", "processor = FlamingoProcessor(model.config)\n", "\n", "print(\"✅ Model loaded successfully!\")\n", "\n", "# Dataset paths - Update these to your actual dataset location\n", "video_folders = {\n", "    0: r\"C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video normale\",\n", "    1: r\"C:\\Users\\<USER>\\Desktop\\video pour mohamed\\video vol\"\n", "}\n", "\n", "# Verify dataset paths exist\n", "for label, folder in video_folders.items():\n", "    if os.path.exists(folder):\n", "        video_count = len([f for f in os.listdir(folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))])\n", "        print(f\"📁 Found {video_count} videos in {'theft' if label == 1 else 'normal'} folder: {folder}\")\n", "    else:\n", "        print(f\"❌ Warning: Folder not found: {folder}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Frame extractor\n", "def extract_frames(video_path, max_frames=8):\n", "    \"\"\"Extract frames from video for analysis\"\"\"\n", "    cap = cv2.VideoCapture(video_path)\n", "    frames = []\n", "    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))\n", "    fps = cap.get(cv2.CAP_PROP_FPS)\n", "    duration = total_frames / fps if fps > 0 else 0\n", "    interval = max(1, total_frames // max_frames)\n", "\n", "    for i in range(0, total_frames, interval):\n", "        cap.set(cv2.CAP_PROP_POS_FRAMES, i)\n", "        ret, frame = cap.read()\n", "        if not ret:\n", "            break\n", "        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "        frames.append(Image.fromarray(rgb))\n", "        if len(frames) >= max_frames:\n", "            break\n", "    cap.release()\n", "    return frames, total_frames, fps, duration\n", "\n", "# Frame classifier using captioning\n", "def classify_frame(image):\n", "    \"\"\"Classify a single frame for shoplifting activity\"\"\"\n", "    try:\n", "        # Use a more descriptive prompt for better results\n", "        prompt = \"<image>Describe what is happening in this image. Is there any suspicious activity, theft, or shoplifting?\"\n", "        \n", "        # Process the image and text\n", "        inputs = processor(images=[image], text=prompt, return_tensors=\"pt\")\n", "        inputs = {k: v.to(device) for k, v in inputs.items()}\n", "        \n", "        # Generate response\n", "        with torch.no_grad():\n", "            outputs = model.generate(**inputs, max_new_tokens=50, do_sample=False, temperature=1.0)\n", "        \n", "        # Decode the response\n", "        response = processor.batch_decode(outputs, skip_special_tokens=True)[0]\n", "        \n", "        # Extract the generated part (after the prompt)\n", "        if prompt in response:\n", "            response_text = response.split(prompt)[-1].strip()\n", "        else:\n", "            response_text = response.strip()\n", "        \n", "        response_text = response_text.lower()\n", "        \n", "        # Enhanced keyword detection for shoplifting\n", "        theft_keywords = [\n", "            \"steal\", \"stealing\", \"shoplifting\", \"theft\", \"thief\", \"robbery\", \"robbing\",\n", "            \"hiding\", \"suspicious\", \"taking\", \"grabbing\", \"pocket\", \"concealing\",\n", "            \"criminal\", \"illegal\", \"unauthorized\", \"without paying\", \"sneaking\"\n", "        ]\n", "        \n", "        # Check for theft indicators\n", "        is_theft = int(any(keyword in response_text for keyword in theft_keywords))\n", "        \n", "        return is_theft, response_text\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in classify_frame: {e}\")\n", "        return 0, f\"Error: {str(e)}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Video classifier (majority vote)\n", "def classify_video(video_path):\n", "    \"\"\"Classify a video for shoplifting activity using majority voting\"\"\"\n", "    frames, total_frames, fps, duration = extract_frames(video_path)\n", "    if not frames:\n", "        return 0, \"No frames extracted\", 0, 0, 0, 0\n", "    \n", "    start_time = time.time()\n", "    preds = []\n", "    responses = []\n", "    for f in frames:\n", "        pred, response = classify_frame(f)\n", "        preds.append(pred)\n", "        responses.append(response)\n", "        \n", "    processing_time = time.time() - start_time\n", "    \n", "    final_prediction = int(sum(preds) > len(preds) / 2)\n", "    \n", "    # Combine all responses for detailed output\n", "    final_response = \" | \".join(responses[:3])  # Show first 3 responses\n", "    \n", "    return final_prediction, final_response, processing_time, total_frames, fps, duration, len(frames)\n", "\n", "print(\"🔧 Functions defined successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluation loop\n", "true_labels = []\n", "pred_labels = []\n", "total_videos = sum(len([f for f in os.listdir(folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]) for folder in video_folders.values() if os.path.exists(folder))\n", "processed_videos = 0\n", "total_time = 0\n", "\n", "print(f\"🎬 Starting evaluation of {total_videos} videos...\")\n", "print(\"=\" * 80)\n", "\n", "for label, folder in video_folders.items():\n", "    if not os.path.exists(folder):\n", "        print(f\"❌ Skipping folder (not found): {folder}\")\n", "        continue\n", "        \n", "    class_name = 'Theft' if label == 1 else 'Normal'\n", "    video_files = [f for f in os.listdir(folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"✅ EVALUATING {class_name.upper()} VIDEOS ({'Positive' if label == 1 else 'Negative'} Class)\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    for video_file in video_files:\n", "        processed_videos += 1\n", "        video_path = os.path.join(folder, video_file)\n", "        \n", "        print(f\"\\n[{processed_videos}/{total_videos}] {'🚨' if label == 1 else '✅'} {class_name} Video: {video_file}\")\n", "        print(\"=\" * 50)\n", "        print(f\"Testing video: {video_path}\")\n", "        print(f\"Question: Is there shoplifting in this video?\")\n", "        print(\"=\" * 50)\n", "        \n", "        pred, response, proc_time, total_frames, fps, duration, extracted_frame_count = classify_video(video_path)\n", "        total_time += proc_time\n", "        \n", "        print(f\"Video info: {total_frames} frames, {fps:.2f} FPS, {duration:.2f}s duration\")\n", "        print(f\"Extracted {extracted_frame_count} frames for analysis\\n\")\n", "        \n", "        print(f\"Response (generated in {proc_time:.2f}s):\")\n", "        print(\"-\" * 40)\n", "        print(response)\n", "        print(\"-\" * 40)\n", "        \n", "        pred_labels.append(pred)\n", "        true_labels.append(label)\n", "        \n", "        ground_truth_str = 'THEFT' if label == 1 else 'NORMAL'\n", "        prediction_str = 'THEFT' if pred == 1 else 'NORMAL'\n", "        correctness_str = '✅ CORRECT' if label == pred else ('❌ FALSE POSITIVE' if label == 0 and pred == 1 else '❌ FALSE NEGATIVE')\n", "        \n", "        print(f\"   Ground Truth: {ground_truth_str} | Prediction: {prediction_str} | {correctness_str}\")\n", "        print(f\"   Processing Time: {proc_time:.2f}s\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute and display final metrics\n", "if len(true_labels) > 0:\n", "    accuracy = accuracy_score(true_labels, pred_labels)\n", "    precision, recall, f1, _ = precision_recall_fscore_support(true_labels, pred_labels, average='binary', zero_division=0)\n", "    \n", "    # Handle case where confusion matrix might not have all classes\n", "    cm = confusion_matrix(true_labels, pred_labels)\n", "    if cm.shape == (2, 2):\n", "        tn, fp, fn, tp = cm.ravel()\n", "    else:\n", "        # Handle cases where only one class is present\n", "        if len(set(true_labels)) == 1 and len(set(pred_labels)) == 1:\n", "            if true_labels[0] == pred_labels[0] == 0:\n", "                tn, fp, fn, tp = len(true_labels), 0, 0, 0\n", "            elif true_labels[0] == pred_labels[0] == 1:\n", "                tn, fp, fn, tp = 0, 0, 0, len(true_labels)\n", "            else:\n", "                tn, fp, fn, tp = 0, 0, 0, 0\n", "        else:\n", "            tn, fp, fn, tp = 0, 0, 0, 0\n", "    \n", "    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0\n", "    avg_time = total_time / len(true_labels) if len(true_labels) > 0 else 0\n", "\n", "    print(\"\\n\" + \"=\" * 80)\n", "    print(\"📊 PERFORMANCE METRICS CALCULATION\")\n", "    print(\"=\" * 80)\n", "    print(\"\\n🎯 FINAL RESULTS:\")\n", "    print(\"=\" * 50)\n", "    print(f\"📈 F1 SCORE:           {f1:.4f} ({f1:.2%})\")\n", "    print(f\"🎯 PRECISION:          {precision:.4f} ({precision:.2%})\")\n", "    print(f\"🔍 RECALL:             {recall:.4f} ({recall:.2%})\")\n", "    print(f\"✅ ACCURACY:           {accuracy:.4f} ({accuracy:.2%})\")\n", "    print(f\"🛡️ SPECIFICITY:        {specificity:.4f} ({specificity:.2%})\")\n", "    print(f\"⏱️ AVG PROCESSING TIME: {avg_time:.2f} seconds/video\")\n", "\n", "    print(\"\\n📊 CONFUSION MATRIX:\")\n", "    print(\"=\" * 30)\n", "    print(\"                 Predicted\")\n", "    print(\"               Normal  Theft\")\n", "    print(f\"Actual Normal    {tn:4d}    {fp:4d}\")\n", "    print(f\"       Theft     {fn:4d}    {tp:4d}\")\n", "\n", "    print(\"\\n📋 DETAILED BREAKDOWN:\")\n", "    print(\"=\" * 40)\n", "    print(f\"✅ True Positives (TP):   {tp} - Theft correctly detected\")\n", "    print(f\"✅ True Negatives (TN):    {tn} - Normal correctly identified\")\n", "    print(f\"❌ False Positives (FP):  {fp} - Normal wrongly flagged as theft\")\n", "    print(f\"❌ False Negatives (FN):   {fn} - Theft missed\")\n", "\n", "    print(\"\\n🚀 EVALUATION COMPLETE!\")\n", "else:\n", "    print(\"❌ No videos were processed. Please check your dataset paths.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 4}